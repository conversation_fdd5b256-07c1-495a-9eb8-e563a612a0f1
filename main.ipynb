import langchain
langchain.__version__

import os

try:
    # load environment variables from .env file (requires `python-dotenv`)
    from dotenv import load_dotenv

    load_dotenv()
except ImportError:
    pass

os.environ["LANGSMITH_TRACING"] = os.getenv("LANGSMITH_TRACING") or "true"
if "LANGSMITH_API_KEY" not in os.environ:
    os.environ["LANGSMITH_API_KEY"] = os.getenv("LANGSMITH_API_KEY")
if "LANGSMITH_PROJECT" not in os.environ:
    os.environ["LANGSMITH_PROJECT"] = os.getenv("LANGSMITH_PROJECT")
if "GOOGLE_API_KEY" not in os.environ:
  print(os.getenv("GOOGLE_API_KEY"))

from langchain.chat_models import init_chat_model

model = init_chat_model("gemini-2.0-flash", model_provider="google_genai")

from langchain_core.messages import HumanMessage, SystemMessage

messages = [
    SystemMessage("Say hi back"),
    HumanMessage("hi!"),
]

res = model.invoke(messages)
res.content

from langchain.prompts import ChatPromptTemplate
template = "Tell me a joke about {name}"
prompt_template = ChatPromptTemplate.from_template(template)

prompt = prompt_template.invoke({"name": "max verstappen"})

model.invoke(prompt)

from langchain.prompts import ChatPromptTemplate
from langchain.schema.output_parser import StrOutputParser
from langchain.schema.runnable import RunnableParallel, RunnableLambda

system_description = "You are a professional Tech product reviewer and are an expert at comparing 2 products in a concise yet deep way"

prompt_template = ChatPromptTemplate.from_messages(
    [
        ("system", system_description),
        ("human", "Compare these 2 products: {product1} vs {product2}"),
    ]
)
prompt_template

def analyse_adv_product1(features):
    adv_product1_template = ChatPromptTemplate.from_messages(
        [
            ("system", system_description),
            ("human", "Given this comparaison between the 2 products: {features}.\n Precise in which areas is product1 better than product2")
        ]
    )
    return adv_product1_template.format_prompt(features=features)

def analyse_adv_product2(features):
    adv_product2_template = ChatPromptTemplate.from_messages(
        [
            ("system", system_description),
            ("human", "Given this comparaison between the 2 products: {features}.\n Precise in which areas is product2 better than product1")
        ]
    )
    return adv_product2_template.format_prompt(features=features)

adv_product1_branch_chain = (
    RunnableLambda(lambda x: analyse_adv_product1(x)) | model | StrOutputParser()
)

adv_product2_branch_chain = (
    RunnableLambda(lambda x: analyse_adv_product2(x)) | model | StrOutputParser()
)

def summarize(features):
    profile = "Software Engineering student searching for versatility, performance, lengevity, compatibility and battery life"
    summary_template = ChatPromptTemplate.from_messages([
        ("system", "You are an unbiased tech reviewer. Summarize the provided results about 2 products"),
        ("human", "Given these 2 comparisons describing the pros and cons of each products {features}. Give a clear winner for a " + profile)
    ])
    return summary_template.format_prompt(features=features)



chain = (
    prompt_template
    | model
    | StrOutputParser()
    | RunnableParallel(branches={
        "adv_product1": adv_product1_branch_chain,
        "adv_product2": adv_product2_branch_chain,
    })
    | StrOutputParser()
    | RunnableLambda(lambda x
    | StrOutputParser()
)

result = chain.invoke({"product1":"samsung galaxy s23", "product2": "iPhone 14"})

result.content