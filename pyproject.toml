[tool.poetry]
name = "langchain-crash-course"
version = "0.1.0"
description = "Everything you need to know to get started with <PERSON><PERSON><PERSON><PERSON>"
authors = ["bhancock_ai <<EMAIL>>"]

[tool.poetry.dependencies]
python = ">=3.10.0,<3.12"
langchain-openai = "^0.1.8"
python-dotenv = "^1.0.1"
langchain = "^0.2.1"
langchain-community = "^0.2.1"
langchain-anthropic = "^0.1.15"
langchain-google-genai = "^1.0.5"
langchain-google-firestore = "^0.3.0"
firestore = "^0.0.8"
chromadb = "^0.5.0"
tiktoken = "^0.7.0"
sentence-transformers = "^3.0.0"
bs4 = "^0.0.2"
firecrawl-py = "^0.0.13"
langchainhub = "^0.1.18"
wikipedia = "^1.4.0"
tavily-python = "^0.3.3"

[tool.pyright]
# https://github.com/microsoft/pyright/blob/main/docs/configuration.md
useLibraryCodeForTypes = true
exclude = [".cache"]

[tool.ruff]
# https://beta.ruff.rs/docs/configuration/
select = ['E', 'W', 'F', 'I', 'B', 'C4', 'ARG', 'SIM']
ignore = ['W291', 'W292', 'W293']

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api" 