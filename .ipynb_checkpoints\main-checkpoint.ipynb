{"cells": [{"cell_type": "code", "execution_count": 2, "id": "1c52ed77-8c6b-4cc3-82e4-93f4168e3da8", "metadata": {}, "outputs": [{"data": {"text/plain": ["'0.3.25'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import langchain\n", "langchain.__version__"]}, {"cell_type": "code", "execution_count": 4, "id": "0267899d-e66a-4ea3-8656-877d5cbafb36", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LANGSMITH_TRACING: None\n", "LANGSMITH_API_KEY: ***************************************************\n", "LANGSMITH_PROJECT: test-project\n"]}], "source": ["from dotenv import load_dotenv\n", "import os\n", "\n", "# Load environment variables from .env file\n", "load_dotenv()\n", "\n", "# Access variables\n", "LANGSMITH_TRACING = os.getenv(\"LANGSMITH_TRACING\", \"False\")\n", "LANGSMITH_API_KEY = os.getenv(\"LANGSMITH_API_KEY\")\n", "LANGSMITH_PROJECT = os.getenv(\"LANGSMITH_PROJECT\")  # Second arg is default value\n", "\n", "print(f\"LANGSMITH_TRACING: {api_key}\")\n", "print(f\"LANGSMITH_API_KEY: {LANGSMITH_API_KEY}\")\n", "print(f\"LANGSMITH_PROJECT: {LANGSMITH_PROJECT}\")"]}, {"cell_type": "code", "execution_count": null, "id": "4899db53-eb6b-4c9c-a1d0-7464cf70cdb7", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "try:\n", "    # load environment variables from .env file (requires `python-dotenv`)\n", "    from dotenv import load_dotenv\n", "\n", "    load_dotenv()\n", "except ImportError:\n", "    pass\n", "\n", "os.environ[\"LANGSMITH_TRACING\"] = LANGSMITH_TRACING or \"true\"\n", "if \"LANGSMITH_API_KEY\" not in os.environ:\n", "    os.environ[\"LANGSMITH_API_KEY\"] = LANGSMITH_API_KEY\n", "if \"LANGSMITH_PROJECT\" not in os.environ:\n", "    os.environ[\"LANGSMITH_PROJECT\"] = LANGSMITH_PROJECT"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 5}