import langchain
langchain.__version__

import os

try:
    # load environment variables from .env file (requires `python-dotenv`)
    from dotenv import load_dotenv

    load_dotenv()
except ImportError:
    pass

from langchain.chat_models import init_chat_model

model = init_chat_model("gemini-2.0-flash", model_provider="google_genai")

from langchain_core.messages import HumanMessage, SystemMessage

while True:
    msg = input("You: ")
    messages = [
        SystemMessage("You're a friendly chatbot and a friend of mine."),
        HumanMessage(msg),
    ]

    res = model.stream(messages)
    for toekn in res:
        print("Gemini:", end="")
        print(toekn.content, end="")
    print("")